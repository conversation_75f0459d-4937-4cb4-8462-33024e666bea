"use client";

import { useState } from 'react';

interface DomainCheckResult {
  Domain: string;
  Available: boolean;
  IsPremiumName: boolean;
  Price?: number;
  Error?: string;
}

const DomainPage = () => {
  const [domainName, setDomainName] = useState('');
  const [results, setResults] = useState<DomainCheckResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [registering, setRegistering] = useState<string | null>(null);

  const handleSearch = async (e: React.MouseEvent | React.KeyboardEvent) => {
    e.preventDefault();
    if (!domainName.trim()) return;

    setLoading(true);
    setResults([]);

    try {
      const response = await fetch('/api/namecheap', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ domain: domainName.trim(), action: 'check' }),
      });

      if (!response.ok) {
        const { error } = await response.json();
        throw new Error(error || 'Failed to check domain availability.');
      }

      const data = await response.json();
      setResults(data.results);
      console.log("data.results",data.results);
    } catch (error) {
      const err = error as Error;
      console.error('Domain search error:', err);
      setResults([{ Domain: domainName, Available: false, IsPremiumName: false, Error: err.message }]);
    } finally {
      setLoading(false);
    }
  };

  const handleRegister = async (domain: string) => {
    setRegistering(domain);
    
    try {
      const response = await fetch('/api/namecheap', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ domain, action: 'register' }),
      });

      if (!response.ok) {
        const { error } = await response.json();
        throw new Error(error || 'Failed to register domain.');
      }

      const data = await response.json();
      
      // Update the results to show the domain as registered
      setResults(prev => prev.map(result => 
        result.Domain === domain 
          ? { ...result, Available: false, Error: undefined }
          : result
      ));

      alert(`Domain ${domain} has been successfully registered!`);
    } catch (error) {
      const err = error as Error;
      console.error('Domain registration error:', err);
      alert(`Failed to register ${domain}: ${err.message}`);
    } finally {
      setRegistering(null);
    }
  };

  return (
    <div className="min-h-screen p-8 bg-gray-50">
      <div className="max-w-4xl mx-auto">
        <h1 className="mb-4 text-4xl font-bold text-gray-800">Register a Domain</h1>
        <p className="mb-8 text-gray-600">Find and register the perfect domain for your new website.</p>

        <div className="flex items-center gap-4 p-6 mb-8 bg-white rounded-lg shadow-sm">
          <input
            type="text"
            value={domainName}
            onChange={(e) => setDomainName(e.target.value)}
            placeholder="Find your new domain (e.g., my-awesome-site.com.au)"
            className="flex-grow p-3 transition border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
            onKeyPress={(e) => e.key === 'Enter' && handleSearch(e)}
          />
          <button
            onClick={handleSearch}
            disabled={loading || !domainName.trim()}
            className="px-6 py-3 font-bold text-white transition bg-green-600 rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            {loading ? 'Searching...' : 'Search'}
          </button>
        </div>

        {results.length > 0 && (
          <div className="p-6 bg-white rounded-lg shadow-sm">
            <h2 className="mb-4 text-2xl font-bold text-gray-800">Results</h2>
            <ul className="space-y-4">
              {results.map((result) => (
                <li key={result.Domain} className="flex items-center justify-between p-4 border rounded-md">
                  <div className="flex flex-col">
                    <span className="text-lg font-medium text-gray-700">{result.Domain}</span>
                    {result.Price && (
                      <span className="text-sm text-gray-500">
                        AUD ${result.Price.toFixed(2)}/year
                        {result.IsPremiumName && <span className="ml-2 text-orange-500 font-semibold">Premium</span>}
                      </span>
                    )}
                  </div>
                  <div className="flex items-center gap-4">
                    {result.Error ? (
                      <span className="font-semibold text-red-500">{result.Error}</span>
                    ) : result.Available ? (
                      <>
                        <span className="font-bold text-green-600">Available!</span>
                        <button
                          onClick={() => handleRegister(result.Domain)}
                          disabled={registering === result.Domain}
                          className="px-4 py-2 font-bold text-white transition bg-blue-600 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed min-w-[100px]"
                        >
                          {registering === result.Domain ? 'Registering...' : 'Register'}
                        </button>
                      </>
                    ) : (
                      <span className="font-semibold text-red-500">Unavailable</span>
                    )}
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}

      </div>
    </div>
  );
};

export default DomainPage;