import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@supabase/supabase-js';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: '2025-06-30.basil',
});

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

interface NamecheapConfig {
  apiUser: string;
  apiKey: string;
  username: string;
  clientIp: string;
  sandbox: boolean;
}

async function registerDomainWithNamecheap(domain: string, years: number = 1): Promise<boolean> {
  const config: NamecheapConfig = {
    apiUser: process.env.NAMECHEAP_API_USER!,
    apiKey: process.env.NAMECHEAP_API_KEY!,
    username: process.env.NAMECHEAP_USERNAME!,
    clientIp: process.env.NAMECHEAP_CLIENT_IP!,
    sandbox: process.env.NAMECHEAP_SANDBOX === 'true',
  };

  if (!config.apiUser || !config.apiKey || !config.username || !config.clientIp) {
    throw new Error('Missing Namecheap API configuration');
  }

  const baseUrl = config.sandbox
    ? 'https://api.sandbox.namecheap.com/xml.response'
    : 'https://api.namecheap.com/xml.response';

  // Extract domain parts
  const domainParts = domain.split('.');
  const sld = domainParts[0]; // Second Level Domain (e.g., "example" from "example.com")
  const tld = domainParts.slice(1).join('.'); // Top Level Domain (e.g., "com" from "example.com")

  const params = new URLSearchParams({
    ApiUser: config.apiUser,
    ApiKey: config.apiKey,
    UserName: config.username,
    Command: 'namecheap.domains.create',
    ClientIp: config.clientIp,
    DomainName: domain,
    Years: years.toString(),
    // Add default contact information (in production, you'd collect this from the user)
    AuxBillingFirstName: 'John',
    AuxBillingLastName: 'Doe',
    AuxBillingAddress1: '123 Main St',
    AuxBillingCity: 'Sydney',
    AuxBillingStateProvince: 'NSW',
    AuxBillingPostalCode: '2000',
    AuxBillingCountry: 'AU',
    AuxBillingPhone: '+61.212345678',
    AuxBillingEmailAddress: '<EMAIL>',
    // Use same info for all contact types for simplicity
    TechFirstName: 'John',
    TechLastName: 'Doe',
    TechAddress1: '123 Main St',
    TechCity: 'Sydney',
    TechStateProvince: 'NSW',
    TechPostalCode: '2000',
    TechCountry: 'AU',
    TechPhone: '+61.212345678',
    TechEmailAddress: '<EMAIL>',
    AdminFirstName: 'John',
    AdminLastName: 'Doe',
    AdminAddress1: '123 Main St',
    AdminCity: 'Sydney',
    AdminStateProvince: 'NSW',
    AdminPostalCode: '2000',
    AdminCountry: 'AU',
    AdminPhone: '+61.212345678',
    AdminEmailAddress: '<EMAIL>',
    RegistrantFirstName: 'John',
    RegistrantLastName: 'Doe',
    RegistrantAddress1: '123 Main St',
    RegistrantCity: 'Sydney',
    RegistrantStateProvince: 'NSW',
    RegistrantPostalCode: '2000',
    RegistrantCountry: 'AU',
    RegistrantPhone: '+61.212345678',
    RegistrantEmailAddress: '<EMAIL>',
  });

  try {
    const response = await fetch(`${baseUrl}?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const xmlText = await response.text();
    
    // Check for API errors
    const errorMatch = xmlText.match(/<Error[^>]*>([^<]*)<\/Error>/);
    if (errorMatch) {
      throw new Error(`Namecheap API error: ${errorMatch[1]}`);
    }

    // Check for successful registration
    const successMatch = xmlText.match(/<DomainCreateResult[^>]*Domain="([^"]*)"[^>]*Registered="([^"]*)"[^>]*\/>/);
    if (successMatch && successMatch[2] === 'true') {
      return true;
    }

    return false;
  } catch (error) {
    console.error('Namecheap domain registration error:', error);
    throw error;
  }
}

export async function POST(req: NextRequest) {
  try {
    const { sessionId } = await req.json();

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    // Retrieve the session from Stripe
    const session = await stripe.checkout.sessions.retrieve(sessionId);

    if (session.payment_status !== 'paid') {
      return NextResponse.json(
        { error: 'Payment not completed' },
        { status: 400 }
      );
    }

    // Extract domain registration details from metadata
    const { domain, siteId, siteName, price } = session.metadata || {};

    if (!domain || !siteId) {
      return NextResponse.json(
        { error: 'Invalid session metadata' },
        { status: 400 }
      );
    }

    try {
      // Register the domain with Namecheap
      console.log(`Registering domain: ${domain}`);
      const registrationSuccess = await registerDomainWithNamecheap(domain, 1);

      if (!registrationSuccess) {
        throw new Error('Domain registration failed');
      }

      // Update the site record in Supabase to associate the domain
      const { error: updateError } = await supabase
        .from('user-websites')
        .update({ 
          custom_domain: domain,
          domain_status: 'registered',
          updated_at: new Date().toISOString()
        })
        .eq('id', siteId);

      if (updateError) {
        console.error('Failed to update site with domain:', updateError);
        // Don't fail the entire process if database update fails
      }

      return NextResponse.json({
        success: true,
        message: `Domain ${domain} has been successfully registered and associated with ${siteName}`,
        domain,
        siteId,
      });

    } catch (registrationError: any) {
      console.error('Domain registration error:', registrationError);
      return NextResponse.json({
        error: `Domain registration failed: ${registrationError.message}. Payment was successful, but we could not register the domain. Our team will contact you.`,
        paymentSuccess: true,
        domain,
        siteId,
      }, { status: 500 });
    }

  } catch (error: any) {
    console.error('Payment verification error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to verify payment' },
      { status: 500 }
    );
  }
}
