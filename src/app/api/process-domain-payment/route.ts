import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@supabase/supabase-js';
import { registerDomain, checkDomainAvailability } from '../namecheap/route';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: '2025-06-30.basil',
});

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

// Configure DNS with CNAME records
const configureDNS = async (domain: string, siteName: string): Promise<boolean> => {
  try {
    // In a production environment, you would use the Namecheap API to set DNS records
    // This would typically involve:
    // 1. Setting up a CNAME record for www.domain.com pointing to siteName.yourdomain.com
    // 2. Setting up an A record for the apex domain (domain.com) pointing to your server IP

    // Example of what a real implementation might look like:
    /*
    const config = {
      apiUser: process.env.NAMECHEAP_API_USER,
      apiKey: process.env.NAMECHEAP_API_KEY,
      username: process.env.NAMECHEAP_USERNAME,
      clientIp: process.env.NAMECHEAP_CLIENT_IP,
      sandbox: process.env.NODE_ENV !== 'production',
    };

    const baseUrl = config.sandbox
      ? 'https://api.sandbox.namecheap.com/xml.response'
      : 'https://api.namecheap.com/xml.response';

    // Set CNAME record for www subdomain
    const cnameDomain = domain.split('.')[0]; // Get the domain name without TLD
    const tld = domain.substring(cnameDomain.length + 1); // Get the TLD (.com, .org, etc.)

    const params = {
      ApiUser: config.apiUser,
      ApiKey: config.apiKey,
      UserName: config.username,
      Command: 'namecheap.domains.dns.setHosts',
      ClientIp: config.clientIp,
      SLD: cnameDomain,
      TLD: tld,
      HostName1: 'www',
      RecordType1: 'CNAME',
      Address1: `${siteName}.yourdomain.com`,
      TTL1: '1800'
    };

    const queryString = Object.entries(params)
      .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
      .join('&');

    const response = await fetch(`${baseUrl}?${queryString}`);
    const xmlText = await response.text();

    // Parse XML response to check success
    const status = xmlText.includes('Status="OK"');
    return status;
    */

    // For now, we'll simulate success in development
    console.log(`Configuring DNS for ${domain} to point to ${siteName}`);
    return true;
  } catch (error) {
    console.error('DNS configuration error:', error);
    return false;
  }
};

export async function POST(req: NextRequest) {
  try {
    const { sessionId, domain } = await req.json();
    
    if (!sessionId || !domain) {
      return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 });
    }

    // 1. Verify the Stripe session
    const session = await stripe.checkout.sessions.retrieve(sessionId);
    
    if (session.payment_status !== 'paid') {
      return NextResponse.json({ error: 'Payment not completed' }, { status: 400 });
    }

    // Extract metadata
    const siteId = session.metadata?.siteId;
    
    if (!siteId) {
      return NextResponse.json({ error: 'Missing site ID in session metadata' }, { status: 400 });
    }

    // 2. Get site information from Supabase
    const { data: siteData, error: siteError } = await supabase
      .from('user-websites')
      .select('site_name')
      .eq('id', siteId)
      .single();

    if (siteError || !siteData) {
      console.error('Site fetch error:', siteError);
      return NextResponse.json({ error: 'Failed to fetch site information' }, { status: 500 });
    }

    // 3. Check domain availability first
    try {
      console.log(`Checking availability for domain: ${domain}`);
      const availabilityResults = await checkDomainAvailability([domain]);

      if (!availabilityResults || availabilityResults.length === 0) {
        throw new Error('Failed to check domain availability');
      }

      const domainResult = availabilityResults[0];
      console.log('Domain availability result:', domainResult);

      if (!domainResult.Available) {
        return NextResponse.json({
          error: `Domain ${domain} is not available for registration. It may have been registered by someone else. Please try a different domain.`,
          paymentSuccess: true,
          domainUnavailable: true
        }, { status: 400 });
      }

      // 4. Register the domain through Namecheap if it's available
      console.log(`Registering domain: ${domain}`);
      const registrationResult = await registerDomain(domain, 1); // Register for 1 year

      if (!registrationResult) {
        throw new Error('Domain registration failed');
      }

      // Immediately return success after registration
      return NextResponse.json({
        success: true,
        message: 'Domain registration completed successfully'
      });
    } catch (regError) {
      console.error('Domain registration error:', regError);
      return NextResponse.json({
        error: `Domain registration failed: ${regError.message}. Payment was successful, but we could not register the domain. Our team will contact you.`,
        paymentSuccess: true
      }, { status: 500 });
    }
  } catch (error: any) {
    console.error('Domain payment processing error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
