import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: '2025-06-30.basil',
});

export async function POST(req: NextRequest) {
  try {
    const { domain, price, siteId, siteName } = await req.json();

    if (!domain || !price || !siteId) {
      return NextResponse.json(
        { error: 'Domain, price, and siteId are required' },
        { status: 400 }
      );
    }

    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

    // Create Stripe checkout session for domain purchase
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'aud',
            product_data: {
              name: `Domain Registration: ${domain}`,
              description: `One-year registration for ${domain}`,
              images: [], // You can add domain/website icons here
            },
            unit_amount: Math.round(price * 100), // <PERSON>e expects cents
          },
          quantity: 1,
        },
      ],
      mode: 'payment', // One-time payment for domain registration
      success_url: `${baseUrl}/dashboard?domainSuccess=true&domain=${encodeURIComponent(domain)}`,
      cancel_url: `${baseUrl}/dashboard/domain`,
      metadata: {
        type: 'domain_registration',
        domain,
        siteId,
        siteName: siteName || '',
        price: price.toString(),
      },
      // Add customer email collection
      customer_email: undefined, // You can pass customer email here if available
      // Optional: Add automatic tax calculation
      automatic_tax: { enabled: false }, // Disable for now, can be enabled later
    });

    return NextResponse.json({
      sessionId: session.id,
      url: session.url,
    });
  } catch (error: any) {
    console.error('Domain checkout session creation error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create checkout session' },
      { status: 500 }
    );
  }
}
