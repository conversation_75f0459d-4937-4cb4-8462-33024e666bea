'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';
import Toast, { ToastProps, ToastContextType } from '@/components/Toast';

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const useToast = (): ToastContextType => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

interface ToastProviderProps {
  children: ReactNode;
}

export const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {
  const [toasts, setToasts] = useState<ToastProps[]>([]);

  const showToast = (toast: Omit<ToastProps, 'id' | 'onClose'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newToast: ToastProps = {
      ...toast,
      id,
      onClose: hideToast,
    };
    setToasts(prev => [...prev, newToast]);
  };

  const hideToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  return (
    <ToastContext.Provider value={{ showToast, hideToast }}>
      {children}
      {/* Render all toasts */}
      {toasts.map((toast, index) => (
        <div
          key={toast.id}
          style={{
            top: `${1 + index * 5}rem`, // Stack toasts vertically
          }}
        >
          <Toast {...toast} />
        </div>
      ))}
    </ToastContext.Provider>
  );
};
