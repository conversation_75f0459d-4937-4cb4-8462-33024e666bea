/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/dashboard/domain/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fdashboard%2Fdomain%2Fpage.tsx&server=false!":
/*!************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fdashboard%2Fdomain%2Fpage.tsx&server=false! ***!
  \************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/domain/page.tsx */ \"(app-pages-browser)/./src/app/dashboard/domain/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lMkZob21lJTJGZGVsbCUyRkRlc2t0b3AlMkZ3cC1haS1hcHAlMkZzcmMlMkZhcHAlMkZkYXNoYm9hcmQlMkZkb21haW4lMkZwYWdlLnRzeCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/NDJjNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL2RlbGwvRGVza3RvcC93cC1haS1hcHAvc3JjL2FwcC9kYXNoYm9hcmQvZG9tYWluL3BhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fdashboard%2Fdomain%2Fpage.tsx&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */ \nif (true) {\n    (function() {\n        \"use strict\";\n        var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n        // ATTENTION\n        // When adding new symbols to this file,\n        // Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n        // The Symbol used to tag the ReactElement-like types.\n        var REACT_ELEMENT_TYPE = Symbol.for(\"react.element\");\n        var REACT_PORTAL_TYPE = Symbol.for(\"react.portal\");\n        var REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\");\n        var REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\");\n        var REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n        var REACT_PROVIDER_TYPE = Symbol.for(\"react.provider\");\n        var REACT_CONTEXT_TYPE = Symbol.for(\"react.context\");\n        var REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\");\n        var REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\");\n        var REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\");\n        var REACT_MEMO_TYPE = Symbol.for(\"react.memo\");\n        var REACT_LAZY_TYPE = Symbol.for(\"react.lazy\");\n        var REACT_OFFSCREEN_TYPE = Symbol.for(\"react.offscreen\");\n        var REACT_CACHE_TYPE = Symbol.for(\"react.cache\");\n        var MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\n        var FAUX_ITERATOR_SYMBOL = \"@@iterator\";\n        function getIteratorFn(maybeIterable) {\n            if (maybeIterable === null || typeof maybeIterable !== \"object\") {\n                return null;\n            }\n            var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n            if (typeof maybeIterator === \"function\") {\n                return maybeIterator;\n            }\n            return null;\n        }\n        var ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n        function error(format) {\n            {\n                {\n                    for(var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++){\n                        args[_key2 - 1] = arguments[_key2];\n                    }\n                    printWarning(\"error\", format, args);\n                }\n            }\n        }\n        function printWarning(level, format, args) {\n            // When changing this logic, you might want to also\n            // update consoleWithStackDev.www.js as well.\n            {\n                var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n                var stack = ReactDebugCurrentFrame.getStackAddendum();\n                if (stack !== \"\") {\n                    format += \"%s\";\n                    args = args.concat([\n                        stack\n                    ]);\n                } // eslint-disable-next-line react-internal/safe-string-coercion\n                var argsWithFormat = args.map(function(item) {\n                    return String(item);\n                }); // Careful: RN currently depends on this prefix\n                argsWithFormat.unshift(\"Warning: \" + format); // We intentionally don't use spread (or .apply) directly because it\n                // breaks IE9: https://github.com/facebook/react/issues/13610\n                // eslint-disable-next-line react-internal/no-production-logging\n                Function.prototype.apply.call(console[level], console, argsWithFormat);\n            }\n        }\n        // -----------------------------------------------------------------------------\n        var enableScopeAPI = false; // Experimental Create Event Handle API.\n        var enableCacheElement = false;\n        var enableTransitionTracing = false; // No known bugs, but needs performance testing\n        var enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\n        // stuff. Intended to enable React core members to more easily debug scheduling\n        // issues in DEV builds.\n        var enableDebugTracing = false;\n        var REACT_CLIENT_REFERENCE$2 = Symbol.for(\"react.client.reference\");\n        function isValidElementType(type) {\n            if (typeof type === \"string\" || typeof type === \"function\") {\n                return true;\n            } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n            if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden || type === REACT_OFFSCREEN_TYPE || enableScopeAPI || enableCacheElement || enableTransitionTracing) {\n                return true;\n            }\n            if (typeof type === \"object\" && type !== null) {\n                if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n                // types supported by any Flight configuration anywhere since\n                // we don't know which Flight build this will end up being used\n                // with.\n                type.$$typeof === REACT_CLIENT_REFERENCE$2 || type.getModuleId !== undefined) {\n                    return true;\n                }\n            }\n            return false;\n        }\n        function getWrappedName(outerType, innerType, wrapperName) {\n            var displayName = outerType.displayName;\n            if (displayName) {\n                return displayName;\n            }\n            var functionName = innerType.displayName || innerType.name || \"\";\n            return functionName !== \"\" ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n        } // Keep in sync with react-reconciler/getComponentNameFromFiber\n        function getContextName(type) {\n            return type.displayName || \"Context\";\n        }\n        var REACT_CLIENT_REFERENCE$1 = Symbol.for(\"react.client.reference\"); // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n        function getComponentNameFromType(type) {\n            if (type == null) {\n                // Host root, text node or just invalid type.\n                return null;\n            }\n            if (typeof type === \"function\") {\n                if (type.$$typeof === REACT_CLIENT_REFERENCE$1) {\n                    // TODO: Create a convention for naming client references with debug info.\n                    return null;\n                }\n                return type.displayName || type.name || null;\n            }\n            if (typeof type === \"string\") {\n                return type;\n            }\n            switch(type){\n                case REACT_FRAGMENT_TYPE:\n                    return \"Fragment\";\n                case REACT_PORTAL_TYPE:\n                    return \"Portal\";\n                case REACT_PROFILER_TYPE:\n                    return \"Profiler\";\n                case REACT_STRICT_MODE_TYPE:\n                    return \"StrictMode\";\n                case REACT_SUSPENSE_TYPE:\n                    return \"Suspense\";\n                case REACT_SUSPENSE_LIST_TYPE:\n                    return \"SuspenseList\";\n                case REACT_CACHE_TYPE:\n                    {\n                        return \"Cache\";\n                    }\n            }\n            if (typeof type === \"object\") {\n                {\n                    if (typeof type.tag === \"number\") {\n                        error(\"Received an unexpected object in getComponentNameFromType(). \" + \"This is likely a bug in React. Please file an issue.\");\n                    }\n                }\n                switch(type.$$typeof){\n                    case REACT_CONTEXT_TYPE:\n                        var context = type;\n                        return getContextName(context) + \".Consumer\";\n                    case REACT_PROVIDER_TYPE:\n                        var provider = type;\n                        return getContextName(provider._context) + \".Provider\";\n                    case REACT_FORWARD_REF_TYPE:\n                        return getWrappedName(type, type.render, \"ForwardRef\");\n                    case REACT_MEMO_TYPE:\n                        var outerName = type.displayName || null;\n                        if (outerName !== null) {\n                            return outerName;\n                        }\n                        return getComponentNameFromType(type.type) || \"Memo\";\n                    case REACT_LAZY_TYPE:\n                        {\n                            var lazyComponent = type;\n                            var payload = lazyComponent._payload;\n                            var init = lazyComponent._init;\n                            try {\n                                return getComponentNameFromType(init(payload));\n                            } catch (x) {\n                                return null;\n                            }\n                        }\n                }\n            }\n            return null;\n        }\n        var assign = Object.assign;\n        // Helpers to patch console.logs to avoid logging during side-effect free\n        // replaying on render function. This currently only patches the object\n        // lazily which won't cover if the log function was extracted eagerly.\n        // We could also eagerly patch the method.\n        var disabledDepth = 0;\n        var prevLog;\n        var prevInfo;\n        var prevWarn;\n        var prevError;\n        var prevGroup;\n        var prevGroupCollapsed;\n        var prevGroupEnd;\n        function disabledLog() {}\n        disabledLog.__reactDisabledLog = true;\n        function disableLogs() {\n            {\n                if (disabledDepth === 0) {\n                    /* eslint-disable react-internal/no-production-logging */ prevLog = console.log;\n                    prevInfo = console.info;\n                    prevWarn = console.warn;\n                    prevError = console.error;\n                    prevGroup = console.group;\n                    prevGroupCollapsed = console.groupCollapsed;\n                    prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n                    var props = {\n                        configurable: true,\n                        enumerable: true,\n                        value: disabledLog,\n                        writable: true\n                    }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n                    Object.defineProperties(console, {\n                        info: props,\n                        log: props,\n                        warn: props,\n                        error: props,\n                        group: props,\n                        groupCollapsed: props,\n                        groupEnd: props\n                    });\n                /* eslint-enable react-internal/no-production-logging */ }\n                disabledDepth++;\n            }\n        }\n        function reenableLogs() {\n            {\n                disabledDepth--;\n                if (disabledDepth === 0) {\n                    /* eslint-disable react-internal/no-production-logging */ var props = {\n                        configurable: true,\n                        enumerable: true,\n                        writable: true\n                    }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n                    Object.defineProperties(console, {\n                        log: assign({}, props, {\n                            value: prevLog\n                        }),\n                        info: assign({}, props, {\n                            value: prevInfo\n                        }),\n                        warn: assign({}, props, {\n                            value: prevWarn\n                        }),\n                        error: assign({}, props, {\n                            value: prevError\n                        }),\n                        group: assign({}, props, {\n                            value: prevGroup\n                        }),\n                        groupCollapsed: assign({}, props, {\n                            value: prevGroupCollapsed\n                        }),\n                        groupEnd: assign({}, props, {\n                            value: prevGroupEnd\n                        })\n                    });\n                /* eslint-enable react-internal/no-production-logging */ }\n                if (disabledDepth < 0) {\n                    error(\"disabledDepth fell below zero. \" + \"This is a bug in React. Please file an issue.\");\n                }\n            }\n        }\n        var ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\n        var prefix;\n        function describeBuiltInComponentFrame(name, source, ownerFn) {\n            {\n                if (prefix === undefined) {\n                    // Extract the VM specific prefix used by each line.\n                    try {\n                        throw Error();\n                    } catch (x) {\n                        var match = x.stack.trim().match(/\\n( *(at )?)/);\n                        prefix = match && match[1] || \"\";\n                    }\n                } // We use the prefix to ensure our stacks line up with native stack frames.\n                return \"\\n\" + prefix + name;\n            }\n        }\n        var reentry = false;\n        var componentFrameCache;\n        {\n            var PossiblyWeakMap = typeof WeakMap === \"function\" ? WeakMap : Map;\n            componentFrameCache = new PossiblyWeakMap();\n        }\n        /**\n * Leverages native browser/VM stack frames to get proper details (e.g.\n * filename, line + col number) for a single component in a component stack. We\n * do this by:\n *   (1) throwing and catching an error in the function - this will be our\n *       control error.\n *   (2) calling the component which will eventually throw an error that we'll\n *       catch - this will be our sample error.\n *   (3) diffing the control and sample error stacks to find the stack frame\n *       which represents our component.\n */ function describeNativeComponentFrame(fn, construct) {\n            // If something asked for a stack inside a fake render, it should get ignored.\n            if (!fn || reentry) {\n                return \"\";\n            }\n            {\n                var frame = componentFrameCache.get(fn);\n                if (frame !== undefined) {\n                    return frame;\n                }\n            }\n            reentry = true;\n            var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe[incompatible-type] It does accept undefined.\n            Error.prepareStackTrace = undefined;\n            var previousDispatcher;\n            {\n                previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n                // for warnings.\n                ReactCurrentDispatcher.current = null;\n                disableLogs();\n            }\n            /**\n   * Finding a common stack frame between sample and control errors can be\n   * tricky given the different types and levels of stack trace truncation from\n   * different JS VMs. So instead we'll attempt to control what that common\n   * frame should be through this object method:\n   * Having both the sample and control errors be in the function under the\n   * `DescribeNativeComponentFrameRoot` property, + setting the `name` and\n   * `displayName` properties of the function ensures that a stack\n   * frame exists that has the method name `DescribeNativeComponentFrameRoot` in\n   * it for both control and sample stacks.\n   */ var RunInRootFrame = {\n                DetermineComponentFrameRoot: function() {\n                    var control;\n                    try {\n                        // This should throw.\n                        if (construct) {\n                            // Something should be setting the props in the constructor.\n                            var Fake = function() {\n                                throw Error();\n                            }; // $FlowFixMe[prop-missing]\n                            Object.defineProperty(Fake.prototype, \"props\", {\n                                set: function() {\n                                    // We use a throwing setter instead of frozen or non-writable props\n                                    // because that won't throw in a non-strict mode function.\n                                    throw Error();\n                                }\n                            });\n                            if (typeof Reflect === \"object\" && Reflect.construct) {\n                                // We construct a different control for this case to include any extra\n                                // frames added by the construct call.\n                                try {\n                                    Reflect.construct(Fake, []);\n                                } catch (x) {\n                                    control = x;\n                                }\n                                Reflect.construct(fn, [], Fake);\n                            } else {\n                                try {\n                                    Fake.call();\n                                } catch (x) {\n                                    control = x;\n                                } // $FlowFixMe[prop-missing] found when upgrading Flow\n                                fn.call(Fake.prototype);\n                            }\n                        } else {\n                            try {\n                                throw Error();\n                            } catch (x) {\n                                control = x;\n                            } // TODO(luna): This will currently only throw if the function component\n                            // tries to access React/ReactDOM/props. We should probably make this throw\n                            // in simple components too\n                            var maybePromise = fn(); // If the function component returns a promise, it's likely an async\n                            // component, which we don't yet support. Attach a noop catch handler to\n                            // silence the error.\n                            // TODO: Implement component stacks for async client components?\n                            if (maybePromise && typeof maybePromise.catch === \"function\") {\n                                maybePromise.catch(function() {});\n                            }\n                        }\n                    } catch (sample) {\n                        // This is inlined manually because closure doesn't do it for us.\n                        if (sample && control && typeof sample.stack === \"string\") {\n                            return [\n                                sample.stack,\n                                control.stack\n                            ];\n                        }\n                    }\n                    return [\n                        null,\n                        null\n                    ];\n                }\n            }; // $FlowFixMe[prop-missing]\n            RunInRootFrame.DetermineComponentFrameRoot.displayName = \"DetermineComponentFrameRoot\";\n            var namePropDescriptor = Object.getOwnPropertyDescriptor(RunInRootFrame.DetermineComponentFrameRoot, \"name\"); // Before ES6, the `name` property was not configurable.\n            if (namePropDescriptor && namePropDescriptor.configurable) {\n                // V8 utilizes a function's `name` property when generating a stack trace.\n                Object.defineProperty(RunInRootFrame.DetermineComponentFrameRoot, // is set to `false`.\n                // $FlowFixMe[cannot-write]\n                \"name\", {\n                    value: \"DetermineComponentFrameRoot\"\n                });\n            }\n            try {\n                var _RunInRootFrame$Deter = RunInRootFrame.DetermineComponentFrameRoot(), sampleStack = _RunInRootFrame$Deter[0], controlStack = _RunInRootFrame$Deter[1];\n                if (sampleStack && controlStack) {\n                    // This extracts the first frame from the sample that isn't also in the control.\n                    // Skipping one frame that we assume is the frame that calls the two.\n                    var sampleLines = sampleStack.split(\"\\n\");\n                    var controlLines = controlStack.split(\"\\n\");\n                    var s = 0;\n                    var c = 0;\n                    while(s < sampleLines.length && !sampleLines[s].includes(\"DetermineComponentFrameRoot\")){\n                        s++;\n                    }\n                    while(c < controlLines.length && !controlLines[c].includes(\"DetermineComponentFrameRoot\")){\n                        c++;\n                    } // We couldn't find our intentionally injected common root frame, attempt\n                    // to find another common root frame by search from the bottom of the\n                    // control stack...\n                    if (s === sampleLines.length || c === controlLines.length) {\n                        s = sampleLines.length - 1;\n                        c = controlLines.length - 1;\n                        while(s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]){\n                            // We expect at least one stack frame to be shared.\n                            // Typically this will be the root most one. However, stack frames may be\n                            // cut off due to maximum stack limits. In this case, one maybe cut off\n                            // earlier than the other. We assume that the sample is longer or the same\n                            // and there for cut off earlier. So we should find the root most frame in\n                            // the sample somewhere in the control.\n                            c--;\n                        }\n                    }\n                    for(; s >= 1 && c >= 0; s--, c--){\n                        // Next we find the first one that isn't the same which should be the\n                        // frame that called our sample function and the control.\n                        if (sampleLines[s] !== controlLines[c]) {\n                            // In V8, the first line is describing the message but other VMs don't.\n                            // If we're about to return the first line, and the control is also on the same\n                            // line, that's a pretty good indicator that our sample threw at same line as\n                            // the control. I.e. before we entered the sample frame. So we ignore this result.\n                            // This can happen if you passed a class to function component, or non-function.\n                            if (s !== 1 || c !== 1) {\n                                do {\n                                    s--;\n                                    c--; // We may still have similar intermediate frames from the construct call.\n                                    // The next one that isn't the same should be our match though.\n                                    if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                                        // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                                        var _frame = \"\\n\" + sampleLines[s].replace(\" at new \", \" at \"); // If our component frame is labeled \"<anonymous>\"\n                                        // but we have a user-provided \"displayName\"\n                                        // splice it in to make the stack more readable.\n                                        if (fn.displayName && _frame.includes(\"<anonymous>\")) {\n                                            _frame = _frame.replace(\"<anonymous>\", fn.displayName);\n                                        }\n                                        if (true) {\n                                            if (typeof fn === \"function\") {\n                                                componentFrameCache.set(fn, _frame);\n                                            }\n                                        } // Return the line we found.\n                                        return _frame;\n                                    }\n                                }while (s >= 1 && c >= 0);\n                            }\n                            break;\n                        }\n                    }\n                }\n            } finally{\n                reentry = false;\n                {\n                    ReactCurrentDispatcher.current = previousDispatcher;\n                    reenableLogs();\n                }\n                Error.prepareStackTrace = previousPrepareStackTrace;\n            } // Fallback to just using the name if we couldn't make it throw.\n            var name = fn ? fn.displayName || fn.name : \"\";\n            var syntheticFrame = name ? describeBuiltInComponentFrame(name) : \"\";\n            {\n                if (typeof fn === \"function\") {\n                    componentFrameCache.set(fn, syntheticFrame);\n                }\n            }\n            return syntheticFrame;\n        }\n        function describeFunctionComponentFrame(fn, source, ownerFn) {\n            {\n                return describeNativeComponentFrame(fn, false);\n            }\n        }\n        function shouldConstruct(Component) {\n            var prototype = Component.prototype;\n            return !!(prototype && prototype.isReactComponent);\n        }\n        function describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n            if (type == null) {\n                return \"\";\n            }\n            if (typeof type === \"function\") {\n                {\n                    return describeNativeComponentFrame(type, shouldConstruct(type));\n                }\n            }\n            if (typeof type === \"string\") {\n                return describeBuiltInComponentFrame(type);\n            }\n            switch(type){\n                case REACT_SUSPENSE_TYPE:\n                    return describeBuiltInComponentFrame(\"Suspense\");\n                case REACT_SUSPENSE_LIST_TYPE:\n                    return describeBuiltInComponentFrame(\"SuspenseList\");\n            }\n            if (typeof type === \"object\") {\n                switch(type.$$typeof){\n                    case REACT_FORWARD_REF_TYPE:\n                        return describeFunctionComponentFrame(type.render);\n                    case REACT_MEMO_TYPE:\n                        // Memo may contain any component type so we recursively resolve it.\n                        return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n                    case REACT_LAZY_TYPE:\n                        {\n                            var lazyComponent = type;\n                            var payload = lazyComponent._payload;\n                            var init = lazyComponent._init;\n                            try {\n                                // Lazy may contain any component type so we recursively resolve it.\n                                return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n                            } catch (x) {}\n                        }\n                }\n            }\n            return \"\";\n        }\n        // $FlowFixMe[method-unbinding]\n        var hasOwnProperty = Object.prototype.hasOwnProperty;\n        var loggedTypeFailures = {};\n        var ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n        function setCurrentlyValidatingElement$1(element) {\n            {\n                if (element) {\n                    var owner = element._owner;\n                    var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n                    ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n                } else {\n                    ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n                }\n            }\n        }\n        function checkPropTypes(typeSpecs, values, location, componentName, element) {\n            {\n                // $FlowFixMe[incompatible-use] This is okay but Flow doesn't know it.\n                var has = Function.call.bind(hasOwnProperty);\n                for(var typeSpecName in typeSpecs){\n                    if (has(typeSpecs, typeSpecName)) {\n                        var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n                        // fail the render phase where it didn't fail before. So we log it.\n                        // After these have been cleaned up, we'll let them throw.\n                        try {\n                            // This is intentionally an invariant that gets caught. It's the same\n                            // behavior as without this statement except with a better message.\n                            if (typeof typeSpecs[typeSpecName] !== \"function\") {\n                                // eslint-disable-next-line react-internal/prod-error-codes\n                                var err = Error((componentName || \"React class\") + \": \" + location + \" type `\" + typeSpecName + \"` is invalid; \" + \"it must be a function, usually from the `prop-types` package, but received `\" + typeof typeSpecs[typeSpecName] + \"`.\" + \"This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");\n                                err.name = \"Invariant Violation\";\n                                throw err;\n                            }\n                            error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, \"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\");\n                        } catch (ex) {\n                            error$1 = ex;\n                        }\n                        if (error$1 && !(error$1 instanceof Error)) {\n                            setCurrentlyValidatingElement$1(element);\n                            error(\"%s: type specification of %s\" + \" `%s` is invalid; the type checker \" + \"function must return `null` or an `Error` but returned a %s. \" + \"You may have forgotten to pass an argument to the type checker \" + \"creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and \" + \"shape all require an argument).\", componentName || \"React class\", location, typeSpecName, typeof error$1);\n                            setCurrentlyValidatingElement$1(null);\n                        }\n                        if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n                            // Only monitor this failure once because there tends to be a lot of the\n                            // same error.\n                            loggedTypeFailures[error$1.message] = true;\n                            setCurrentlyValidatingElement$1(element);\n                            error(\"Failed %s type: %s\", location, error$1.message);\n                            setCurrentlyValidatingElement$1(null);\n                        }\n                    }\n                }\n            }\n        }\n        var isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n        function isArray(a) {\n            return isArrayImpl(a);\n        }\n        /*\n * The `'' + value` pattern (used in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */ // $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\n        function typeName(value) {\n            {\n                // toStringTag is needed for namespaced types like Temporal.Instant\n                var hasToStringTag = typeof Symbol === \"function\" && Symbol.toStringTag;\n                var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || \"Object\"; // $FlowFixMe[incompatible-return]\n                return type;\n            }\n        } // $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\n        function willCoercionThrow(value) {\n            {\n                try {\n                    testStringCoercion(value);\n                    return false;\n                } catch (e) {\n                    return true;\n                }\n            }\n        }\n        function testStringCoercion(value) {\n            // If you ended up here by following an exception call stack, here's what's\n            // happened: you supplied an object or symbol value to React (as a prop, key,\n            // DOM attribute, CSS property, string ref, etc.) and when React tried to\n            // coerce it to a string using `'' + value`, an exception was thrown.\n            //\n            // The most common types that will cause this exception are `Symbol` instances\n            // and Temporal objects like `Temporal.Instant`. But any object that has a\n            // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n            // exception. (Library authors do this to prevent users from using built-in\n            // numeric operators like `+` or comparison operators like `>=` because custom\n            // methods are needed to perform accurate arithmetic or comparison.)\n            //\n            // To fix the problem, coerce this object or symbol value to a string before\n            // passing it to React. The most reliable way is usually `String(value)`.\n            //\n            // To find which value is throwing, check the browser or debugger console.\n            // Before this exception was thrown, there should be `console.error` output\n            // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n            // problem and how that type was used: key, atrribute, input value prop, etc.\n            // In most cases, this console output also shows the component and its\n            // ancestor components where the exception happened.\n            //\n            // eslint-disable-next-line react-internal/safe-string-coercion\n            return \"\" + value;\n        }\n        function checkKeyStringCoercion(value) {\n            {\n                if (willCoercionThrow(value)) {\n                    error(\"The provided key is an unsupported type %s.\" + \" This value must be coerced to a string before using it here.\", typeName(value));\n                    return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n                }\n            }\n        }\n        var ReactCurrentOwner$1 = ReactSharedInternals.ReactCurrentOwner;\n        var RESERVED_PROPS = {\n            key: true,\n            ref: true,\n            __self: true,\n            __source: true\n        };\n        var specialPropKeyWarningShown;\n        var specialPropRefWarningShown;\n        var didWarnAboutStringRefs;\n        {\n            didWarnAboutStringRefs = {};\n        }\n        function hasValidRef(config) {\n            {\n                if (hasOwnProperty.call(config, \"ref\")) {\n                    var getter = Object.getOwnPropertyDescriptor(config, \"ref\").get;\n                    if (getter && getter.isReactWarning) {\n                        return false;\n                    }\n                }\n            }\n            return config.ref !== undefined;\n        }\n        function hasValidKey(config) {\n            {\n                if (hasOwnProperty.call(config, \"key\")) {\n                    var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n                    if (getter && getter.isReactWarning) {\n                        return false;\n                    }\n                }\n            }\n            return config.key !== undefined;\n        }\n        function warnIfStringRefCannotBeAutoConverted(config, self) {\n            {\n                if (typeof config.ref === \"string\" && ReactCurrentOwner$1.current && self && ReactCurrentOwner$1.current.stateNode !== self) {\n                    var componentName = getComponentNameFromType(ReactCurrentOwner$1.current.type);\n                    if (!didWarnAboutStringRefs[componentName]) {\n                        error('Component \"%s\" contains the string ref \"%s\". ' + \"Support for string refs will be removed in a future major release. \" + \"This case cannot be automatically converted to an arrow function. \" + \"We ask you to manually fix this case by using useRef() or createRef() instead. \" + \"Learn more about using refs safely here: \" + \"https://reactjs.org/link/strict-mode-string-ref\", getComponentNameFromType(ReactCurrentOwner$1.current.type), config.ref);\n                        didWarnAboutStringRefs[componentName] = true;\n                    }\n                }\n            }\n        }\n        function defineKeyPropWarningGetter(props, displayName) {\n            {\n                var warnAboutAccessingKey = function() {\n                    if (!specialPropKeyWarningShown) {\n                        specialPropKeyWarningShown = true;\n                        error(\"%s: `key` is not a prop. Trying to access it will result \" + \"in `undefined` being returned. If you need to access the same \" + \"value within the child component, you should pass it as a different \" + \"prop. (https://reactjs.org/link/special-props)\", displayName);\n                    }\n                };\n                warnAboutAccessingKey.isReactWarning = true;\n                Object.defineProperty(props, \"key\", {\n                    get: warnAboutAccessingKey,\n                    configurable: true\n                });\n            }\n        }\n        function defineRefPropWarningGetter(props, displayName) {\n            {\n                var warnAboutAccessingRef = function() {\n                    if (!specialPropRefWarningShown) {\n                        specialPropRefWarningShown = true;\n                        error(\"%s: `ref` is not a prop. Trying to access it will result \" + \"in `undefined` being returned. If you need to access the same \" + \"value within the child component, you should pass it as a different \" + \"prop. (https://reactjs.org/link/special-props)\", displayName);\n                    }\n                };\n                warnAboutAccessingRef.isReactWarning = true;\n                Object.defineProperty(props, \"ref\", {\n                    get: warnAboutAccessingRef,\n                    configurable: true\n                });\n            }\n        }\n        /**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */ function ReactElement(type, key, ref, self, source, owner, props) {\n            var element = {\n                // This tag allows us to uniquely identify this as a React Element\n                $$typeof: REACT_ELEMENT_TYPE,\n                // Built-in properties that belong on the element\n                type: type,\n                key: key,\n                ref: ref,\n                props: props,\n                // Record the component responsible for creating this element.\n                _owner: owner\n            };\n            {\n                // The validation flag is currently mutative. We put it on\n                // an external backing store so that we can freeze the whole object.\n                // This can be replaced with a WeakMap once they are implemented in\n                // commonly used development environments.\n                element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n                // the validation flag non-enumerable (where possible, which should\n                // include every environment we run tests in), so the test framework\n                // ignores it.\n                Object.defineProperty(element._store, \"validated\", {\n                    configurable: false,\n                    enumerable: false,\n                    writable: true,\n                    value: false\n                }); // self and source are DEV only properties.\n                Object.defineProperty(element, \"_self\", {\n                    configurable: false,\n                    enumerable: false,\n                    writable: false,\n                    value: self\n                }); // Two elements created in two different places should be considered\n                // equal for testing purposes and therefore we hide it from enumeration.\n                Object.defineProperty(element, \"_source\", {\n                    configurable: false,\n                    enumerable: false,\n                    writable: false,\n                    value: source\n                });\n                if (Object.freeze) {\n                    Object.freeze(element.props);\n                    Object.freeze(element);\n                }\n            }\n            return element;\n        }\n        /**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */ function jsxDEV$1(type, config, maybeKey, source, self) {\n            {\n                var propName; // Reserved names are extracted\n                var props = {};\n                var key = null;\n                var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n                // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n                // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n                // but as an intermediary step, we will use jsxDEV for everything except\n                // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n                // key is explicitly declared to be undefined or not.\n                if (maybeKey !== undefined) {\n                    {\n                        checkKeyStringCoercion(maybeKey);\n                    }\n                    key = \"\" + maybeKey;\n                }\n                if (hasValidKey(config)) {\n                    {\n                        checkKeyStringCoercion(config.key);\n                    }\n                    key = \"\" + config.key;\n                }\n                if (hasValidRef(config)) {\n                    ref = config.ref;\n                    warnIfStringRefCannotBeAutoConverted(config, self);\n                } // Remaining properties are added to a new props object\n                for(propName in config){\n                    if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n                        props[propName] = config[propName];\n                    }\n                } // Resolve default props\n                if (type && type.defaultProps) {\n                    var defaultProps = type.defaultProps;\n                    for(propName in defaultProps){\n                        if (props[propName] === undefined) {\n                            props[propName] = defaultProps[propName];\n                        }\n                    }\n                }\n                if (key || ref) {\n                    var displayName = typeof type === \"function\" ? type.displayName || type.name || \"Unknown\" : type;\n                    if (key) {\n                        defineKeyPropWarningGetter(props, displayName);\n                    }\n                    if (ref) {\n                        defineRefPropWarningGetter(props, displayName);\n                    }\n                }\n                return ReactElement(type, key, ref, self, source, ReactCurrentOwner$1.current, props);\n            }\n        }\n        var ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\n        var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n        var REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\");\n        function setCurrentlyValidatingElement(element) {\n            {\n                if (element) {\n                    var owner = element._owner;\n                    var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n                    ReactDebugCurrentFrame.setExtraStackFrame(stack);\n                } else {\n                    ReactDebugCurrentFrame.setExtraStackFrame(null);\n                }\n            }\n        }\n        var propTypesMisspellWarningShown;\n        {\n            propTypesMisspellWarningShown = false;\n        }\n        /**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */ function isValidElement(object) {\n            {\n                return typeof object === \"object\" && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n            }\n        }\n        function getDeclarationErrorAddendum() {\n            {\n                if (ReactCurrentOwner.current) {\n                    var name = getComponentNameFromType(ReactCurrentOwner.current.type);\n                    if (name) {\n                        return \"\\n\\nCheck the render method of `\" + name + \"`.\";\n                    }\n                }\n                return \"\";\n            }\n        }\n        function getSourceInfoErrorAddendum(source) {\n            {\n                if (source !== undefined) {\n                    var fileName = source.fileName.replace(/^.*[\\\\\\/]/, \"\");\n                    var lineNumber = source.lineNumber;\n                    return \"\\n\\nCheck your code at \" + fileName + \":\" + lineNumber + \".\";\n                }\n                return \"\";\n            }\n        }\n        /**\n * Warn if there's no key explicitly set on dynamic arrays of children or\n * object keys are not valid. This allows us to keep track of children between\n * updates.\n */ var ownerHasKeyUseWarning = {};\n        function getCurrentComponentErrorInfo(parentType) {\n            {\n                var info = getDeclarationErrorAddendum();\n                if (!info) {\n                    var parentName = getComponentNameFromType(parentType);\n                    if (parentName) {\n                        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n                    }\n                }\n                return info;\n            }\n        }\n        /**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */ function validateExplicitKey(element, parentType) {\n            {\n                if (!element._store || element._store.validated || element.key != null) {\n                    return;\n                }\n                element._store.validated = true;\n                var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n                if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n                    return;\n                }\n                ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n                // property, it may be the creator of the child that's responsible for\n                // assigning it a key.\n                var childOwner = \"\";\n                if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n                    // Give the component that originally created this child.\n                    childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n                }\n                setCurrentlyValidatingElement(element);\n                error('Each child in a list should have a unique \"key\" prop.' + \"%s%s See https://reactjs.org/link/warning-keys for more information.\", currentComponentErrorInfo, childOwner);\n                setCurrentlyValidatingElement(null);\n            }\n        }\n        /**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */ function validateChildKeys(node, parentType) {\n            {\n                if (typeof node !== \"object\" || !node) {\n                    return;\n                }\n                if (node.$$typeof === REACT_CLIENT_REFERENCE) ;\n                else if (isArray(node)) {\n                    for(var i = 0; i < node.length; i++){\n                        var child = node[i];\n                        if (isValidElement(child)) {\n                            validateExplicitKey(child, parentType);\n                        }\n                    }\n                } else if (isValidElement(node)) {\n                    // This element was passed in a valid location.\n                    if (node._store) {\n                        node._store.validated = true;\n                    }\n                } else {\n                    var iteratorFn = getIteratorFn(node);\n                    if (typeof iteratorFn === \"function\") {\n                        // Entry iterators used to provide implicit keys,\n                        // but now we print a separate warning for them later.\n                        if (iteratorFn !== node.entries) {\n                            var iterator = iteratorFn.call(node);\n                            var step;\n                            while(!(step = iterator.next()).done){\n                                if (isValidElement(step.value)) {\n                                    validateExplicitKey(step.value, parentType);\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n        }\n        /**\n * Given an element, validate that its props follow the propTypes definition,\n * provided by the type.\n *\n * @param {ReactElement} element\n */ function validatePropTypes(element) {\n            {\n                var type = element.type;\n                if (type === null || type === undefined || typeof type === \"string\") {\n                    return;\n                }\n                if (type.$$typeof === REACT_CLIENT_REFERENCE) {\n                    return;\n                }\n                var propTypes;\n                if (typeof type === \"function\") {\n                    propTypes = type.propTypes;\n                } else if (typeof type === \"object\" && (type.$$typeof === REACT_FORWARD_REF_TYPE || // Note: Memo only checks outer props here.\n                // Inner props are checked in the reconciler.\n                type.$$typeof === REACT_MEMO_TYPE)) {\n                    propTypes = type.propTypes;\n                } else {\n                    return;\n                }\n                if (propTypes) {\n                    // Intentionally inside to avoid triggering lazy initializers:\n                    var name = getComponentNameFromType(type);\n                    checkPropTypes(propTypes, element.props, \"prop\", name, element);\n                } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n                    propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n                    var _name = getComponentNameFromType(type);\n                    error(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\", _name || \"Unknown\");\n                }\n                if (typeof type.getDefaultProps === \"function\" && !type.getDefaultProps.isReactClassApproved) {\n                    error(\"getDefaultProps is only used on classic React.createClass \" + \"definitions. Use a static property named `defaultProps` instead.\");\n                }\n            }\n        }\n        /**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */ function validateFragmentProps(fragment) {\n            {\n                var keys = Object.keys(fragment.props);\n                for(var i = 0; i < keys.length; i++){\n                    var key = keys[i];\n                    if (key !== \"children\" && key !== \"key\") {\n                        setCurrentlyValidatingElement(fragment);\n                        error(\"Invalid prop `%s` supplied to `React.Fragment`. \" + \"React.Fragment can only have `key` and `children` props.\", key);\n                        setCurrentlyValidatingElement(null);\n                        break;\n                    }\n                }\n                if (fragment.ref !== null) {\n                    setCurrentlyValidatingElement(fragment);\n                    error(\"Invalid attribute `ref` supplied to `React.Fragment`.\");\n                    setCurrentlyValidatingElement(null);\n                }\n            }\n        }\n        var didWarnAboutKeySpread = {};\n        function jsxWithValidation(type, props, key, isStaticChildren, source, self) {\n            {\n                var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n                // succeed and there will likely be errors in render.\n                if (!validType) {\n                    var info = \"\";\n                    if (type === undefined || typeof type === \"object\" && type !== null && Object.keys(type).length === 0) {\n                        info += \" You likely forgot to export your component from the file \" + \"it's defined in, or you might have mixed up default and named imports.\";\n                    }\n                    var sourceInfo = getSourceInfoErrorAddendum(source);\n                    if (sourceInfo) {\n                        info += sourceInfo;\n                    } else {\n                        info += getDeclarationErrorAddendum();\n                    }\n                    var typeString;\n                    if (type === null) {\n                        typeString = \"null\";\n                    } else if (isArray(type)) {\n                        typeString = \"array\";\n                    } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n                        typeString = \"<\" + (getComponentNameFromType(type.type) || \"Unknown\") + \" />\";\n                        info = \" Did you accidentally export a JSX literal instead of a component?\";\n                    } else {\n                        typeString = typeof type;\n                    }\n                    error(\"React.jsx: type is invalid -- expected a string (for \" + \"built-in components) or a class/function (for composite \" + \"components) but got: %s.%s\", typeString, info);\n                }\n                var element = jsxDEV$1(type, props, key, source, self); // The result can be nullish if a mock or a custom function is used.\n                // TODO: Drop this when these are no longer allowed as the type argument.\n                if (element == null) {\n                    return element;\n                } // Skip key warning if the type isn't valid since our key validation logic\n                // doesn't expect a non-string/function type and can throw confusing errors.\n                // We don't want exception behavior to differ between dev and prod.\n                // (Rendering will throw with a helpful message and as soon as the type is\n                // fixed, the key warnings will appear.)\n                if (validType) {\n                    var children = props.children;\n                    if (children !== undefined) {\n                        if (isStaticChildren) {\n                            if (isArray(children)) {\n                                for(var i = 0; i < children.length; i++){\n                                    validateChildKeys(children[i], type);\n                                }\n                                if (Object.freeze) {\n                                    Object.freeze(children);\n                                }\n                            } else {\n                                error(\"React.jsx: Static children should always be an array. \" + \"You are likely explicitly calling React.jsxs or React.jsxDEV. \" + \"Use the Babel transform instead.\");\n                            }\n                        } else {\n                            validateChildKeys(children, type);\n                        }\n                    }\n                }\n                if (hasOwnProperty.call(props, \"key\")) {\n                    var componentName = getComponentNameFromType(type);\n                    var keys = Object.keys(props).filter(function(k) {\n                        return k !== \"key\";\n                    });\n                    var beforeExample = keys.length > 0 ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\" : \"{key: someKey}\";\n                    if (!didWarnAboutKeySpread[componentName + beforeExample]) {\n                        var afterExample = keys.length > 0 ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\";\n                        error('A props object containing a \"key\" prop is being spread into JSX:\\n' + \"  let props = %s;\\n\" + \"  <%s {...props} />\\n\" + \"React keys must be passed directly to JSX without using spread:\\n\" + \"  let props = %s;\\n\" + \"  <%s key={someKey} {...props} />\", beforeExample, componentName, afterExample, componentName);\n                        didWarnAboutKeySpread[componentName + beforeExample] = true;\n                    }\n                }\n                if (type === REACT_FRAGMENT_TYPE) {\n                    validateFragmentProps(element);\n                } else {\n                    validatePropTypes(element);\n                }\n                return element;\n            }\n        } // These two functions exist to still get child warnings in dev\n        var jsxDEV = jsxWithValidation;\n        exports.Fragment = REACT_FRAGMENT_TYPE;\n        exports.jsxDEV = jsxDEV;\n    })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nif (false) {} else {\n    module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBRUEsSUFBSUEsS0FBeUIsRUFBYyxFQUUxQyxNQUFNO0lBQ0xDLDhMQUF5QjtBQUMzQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL3JlYWN0L2pzeC1kZXYtcnVudGltZS5qcz9jYTIwIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5taW4uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOlsicHJvY2VzcyIsIm1vZHVsZSIsImV4cG9ydHMiLCJyZXF1aXJlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/domain/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/dashboard/domain/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst DomainPage = ()=>{\n    _s();\n    const [domainName, setDomainName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [registering, setRegistering] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleSearch = async (e)=>{\n        e.preventDefault();\n        if (!domainName.trim()) return;\n        setLoading(true);\n        setResults([]);\n        try {\n            const response = await fetch(\"/api/namecheap\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    domain: domainName.trim(),\n                    action: \"check\"\n                })\n            });\n            if (!response.ok) {\n                const { error } = await response.json();\n                throw new Error(error || \"Failed to check domain availability.\");\n            }\n            const data = await response.json();\n            setResults(data.results);\n            console.log(\"data.results\", data.results);\n        } catch (error) {\n            const err = error;\n            console.error(\"Domain search error:\", err);\n            setResults([\n                {\n                    Domain: domainName,\n                    Available: false,\n                    IsPremiumName: false,\n                    Error: err.message\n                }\n            ]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleRegister = async (domain)=>{\n        setRegistering(domain);\n        try {\n            const response = await fetch(\"/api/namecheap\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    domain,\n                    action: \"register\"\n                })\n            });\n            if (!response.ok) {\n                const { error } = await response.json();\n                throw new Error(error || \"Failed to register domain.\");\n            }\n            const data = await response.json();\n            // Update the results to show the domain as registered\n            setResults((prev)=>prev.map((result)=>result.Domain === domain ? {\n                        ...result,\n                        Available: false,\n                        Error: undefined\n                    } : result));\n            alert(\"Domain \".concat(domain, \" has been successfully registered!\"));\n        } catch (error) {\n            const err = error;\n            console.error(\"Domain registration error:\", err);\n            alert(\"Failed to register \".concat(domain, \": \").concat(err.message));\n        } finally{\n            setRegistering(null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-8 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"mb-4 text-4xl font-bold text-gray-800\",\n                    children: \"Register a Domain\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-8 text-gray-600\",\n                    children: \"Find and register the perfect domain for your new website.\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4 p-6 mb-8 bg-white rounded-lg shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: domainName,\n                            onChange: (e)=>setDomainName(e.target.value),\n                            placeholder: \"Find your new domain (e.g., my-awesome-site.com.au)\",\n                            className: \"flex-grow p-3 transition border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500\",\n                            onKeyPress: (e)=>e.key === \"Enter\" && handleSearch(e)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleSearch,\n                            disabled: loading || !domainName.trim(),\n                            className: \"px-6 py-3 font-bold text-white transition bg-green-600 rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed\",\n                            children: loading ? \"Searching...\" : \"Search\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, undefined),\n                results.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 bg-white rounded-lg shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mb-4 text-2xl font-bold text-gray-800\",\n                            children: \"Results\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"space-y-4\",\n                            children: results.map((result)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"flex items-center justify-between p-4 border rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-lg font-medium text-gray-700\",\n                                                    children: result.Domain\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                result.Price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        \"AUD $\",\n                                                        result.Price.toFixed(2),\n                                                        \"/year\",\n                                                        result.IsPremiumName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 text-orange-500 font-semibold\",\n                                                            children: \"Premium\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                            lineNumber: 119,\n                                                            columnNumber: 50\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: result.Error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-red-500\",\n                                                children: result.Error\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 23\n                                            }, undefined) : result.Available ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold text-green-600\",\n                                                        children: \"Available!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleRegister(result.Domain),\n                                                        disabled: registering === result.Domain,\n                                                        className: \"px-4 py-2 font-bold text-white transition bg-blue-600 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed min-w-[100px]\",\n                                                        children: registering === result.Domain ? \"Registering...\" : \"Register\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-red-500\",\n                                                children: \"Unavailable\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, result.Domain, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DomainPage, \"SguwXD90BUr3QCRD11uI1cdCZ1I=\");\n_c = DomainPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DomainPage);\nvar _c;\n$RefreshReg$(_c, \"DomainPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/domain/page.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fdashboard%2Fdomain%2Fpage.tsx&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);