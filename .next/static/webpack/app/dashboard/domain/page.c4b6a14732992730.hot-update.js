"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/domain/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/domain/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/dashboard/domain/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst DomainPage = ()=>{\n    _s();\n    const [domainName, setDomainName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [registering, setRegistering] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleSearch = async (e)=>{\n        e.preventDefault();\n        if (!domainName) return;\n        setLoading(true);\n        setResults([]);\n        try {\n            const response = await fetch(\"/api/namecheap\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    domain: domainName,\n                    action: \"check\"\n                })\n            });\n            if (!response.ok) {\n                const { error } = await response.json();\n                throw new Error(error || \"Failed to check domain availability.\");\n            }\n            const data = await response.json();\n            setResults(data.results);\n        } catch (error) {\n            const err = error;\n            console.error(\"Domain search error:\", err);\n            setResults([\n                {\n                    Domain: domainName,\n                    Available: false,\n                    IsPremiumName: false,\n                    Error: err.message\n                }\n            ]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleRegister = async (domain)=>{\n        setRegistering(domain);\n        try {\n            const response = await fetch(\"/api/namecheap\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    domain,\n                    action: \"register\"\n                })\n            });\n            if (!response.ok) {\n                const { error } = await response.json();\n                throw new Error(error || \"Failed to register domain.\");\n            }\n            const data = await response.json();\n            // Update the results to show the domain as registered\n            setResults((prev)=>prev.map((result)=>result.Domain === domain ? {\n                        ...result,\n                        Available: false,\n                        Error: undefined\n                    } : result));\n            alert(\"Domain \".concat(domain, \" has been successfully registered!\"));\n        } catch (error) {\n            const err = error;\n            console.error(\"Domain registration error:\", err);\n            alert(\"Failed to register \".concat(domain, \": \").concat(err.message));\n        } finally{\n            setRegistering(null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-8 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"mb-4 text-4xl font-bold text-gray-800\",\n                    children: \"Register a Domain\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-8 text-gray-600\",\n                    children: \"Find and register the perfect domain for your new website.\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4 p-6 mb-8 bg-white rounded-lg shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: domainName,\n                            onChange: (e)=>setDomainName(e.target.value),\n                            placeholder: \"Find your new domain (e.g., my-awesome-site.com.au)\",\n                            className: \"flex-grow p-3 transition border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500\",\n                            onKeyPress: (e)=>e.key === \"Enter\" && handleSearch(e)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleSearch,\n                            disabled: loading,\n                            className: \"px-6 py-3 font-bold text-white transition bg-green-600 rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed\",\n                            children: loading ? \"Searching...\" : \"Search\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, undefined),\n                results.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 bg-white rounded-lg shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mb-4 text-2xl font-bold text-gray-800\",\n                            children: \"Results\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"space-y-4\",\n                            children: results.map((result)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"flex items-center justify-between p-4 border rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-lg font-medium text-gray-700\",\n                                                    children: result.Domain\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                result.Price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        \"$\",\n                                                        result.Price.toFixed(2),\n                                                        \"/year\",\n                                                        result.IsPremiumName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 text-orange-500\",\n                                                            children: \"Premium\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 50\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        result.Error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-red-500\",\n                                            children: result.Error\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 21\n                                        }, undefined) : result.Available ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-green-600\",\n                                                    children: \"Available!\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleRegister(result.Domain),\n                                                    disabled: registering === result.Domain,\n                                                    className: \"px-4 py-2 font-bold text-white transition bg-blue-600 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed\",\n                                                    children: registering === result.Domain ? \"Registering...\" : \"Register\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 21\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-red-500\",\n                                            children: \"Unavailable\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, result.Domain, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n            lineNumber: 85,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DomainPage, \"SguwXD90BUr3QCRD11uI1cdCZ1I=\");\n_c = DomainPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DomainPage);\nvar _c;\n$RefreshReg$(_c, \"DomainPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/domain/page.tsx\n"));

/***/ })

});