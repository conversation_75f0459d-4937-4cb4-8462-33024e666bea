"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@stripe";
exports.ids = ["vendor-chunks/@stripe"];
exports.modules = {

/***/ "(ssr)/./node_modules/@stripe/stripe-js/dist/index.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/@stripe/stripe-js/dist/index.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadStripe: () => (/* binding */ loadStripe)\n/* harmony export */ });\nvar RELEASE_TRAIN = \"basil\";\nvar runtimeVersionToUrlVersion = function runtimeVersionToUrlVersion(version) {\n    return version === 3 ? \"v3\" : version;\n};\nvar ORIGIN = \"https://js.stripe.com\";\nvar STRIPE_JS_URL = \"\".concat(ORIGIN, \"/\").concat(RELEASE_TRAIN, \"/stripe.js\");\nvar V3_URL_REGEX = /^https:\\/\\/js\\.stripe\\.com\\/v3\\/?(\\?.*)?$/;\nvar STRIPE_JS_URL_REGEX = /^https:\\/\\/js\\.stripe\\.com\\/(v3|[a-z]+)\\/stripe\\.js(\\?.*)?$/;\nvar EXISTING_SCRIPT_MESSAGE = \"loadStripe.setLoadParameters was called but an existing Stripe.js script already exists in the document; existing script parameters will be used\";\nvar isStripeJSURL = function isStripeJSURL(url) {\n    return V3_URL_REGEX.test(url) || STRIPE_JS_URL_REGEX.test(url);\n};\nvar findScript = function findScript() {\n    var scripts = document.querySelectorAll('script[src^=\"'.concat(ORIGIN, '\"]'));\n    for(var i = 0; i < scripts.length; i++){\n        var script = scripts[i];\n        if (!isStripeJSURL(script.src)) {\n            continue;\n        }\n        return script;\n    }\n    return null;\n};\nvar injectScript = function injectScript(params) {\n    var queryString = params && !params.advancedFraudSignals ? \"?advancedFraudSignals=false\" : \"\";\n    var script = document.createElement(\"script\");\n    script.src = \"\".concat(STRIPE_JS_URL).concat(queryString);\n    var headOrBody = document.head || document.body;\n    if (!headOrBody) {\n        throw new Error(\"Expected document.body not to be null. Stripe.js requires a <body> element.\");\n    }\n    headOrBody.appendChild(script);\n    return script;\n};\nvar registerWrapper = function registerWrapper(stripe, startTime) {\n    if (!stripe || !stripe._registerWrapper) {\n        return;\n    }\n    stripe._registerWrapper({\n        name: \"stripe-js\",\n        version: \"7.4.0\",\n        startTime: startTime\n    });\n};\nvar stripePromise$1 = null;\nvar onErrorListener = null;\nvar onLoadListener = null;\nvar onError = function onError(reject) {\n    return function(cause) {\n        reject(new Error(\"Failed to load Stripe.js\", {\n            cause: cause\n        }));\n    };\n};\nvar onLoad = function onLoad(resolve, reject) {\n    return function() {\n        if (window.Stripe) {\n            resolve(window.Stripe);\n        } else {\n            reject(new Error(\"Stripe.js not available\"));\n        }\n    };\n};\nvar loadScript = function loadScript(params) {\n    // Ensure that we only attempt to load Stripe.js at most once\n    if (stripePromise$1 !== null) {\n        return stripePromise$1;\n    }\n    stripePromise$1 = new Promise(function(resolve, reject) {\n        if (true) {\n            // Resolve to null when imported server side. This makes the module\n            // safe to import in an isomorphic code base.\n            resolve(null);\n            return;\n        }\n        if (window.Stripe && params) {\n            console.warn(EXISTING_SCRIPT_MESSAGE);\n        }\n        if (window.Stripe) {\n            resolve(window.Stripe);\n            return;\n        }\n        try {\n            var script = findScript();\n            if (script && params) {\n                console.warn(EXISTING_SCRIPT_MESSAGE);\n            } else if (!script) {\n                script = injectScript(params);\n            } else if (script && onLoadListener !== null && onErrorListener !== null) {\n                var _script$parentNode;\n                // remove event listeners\n                script.removeEventListener(\"load\", onLoadListener);\n                script.removeEventListener(\"error\", onErrorListener); // if script exists, but we are reloading due to an error,\n                // reload script to trigger 'load' event\n                (_script$parentNode = script.parentNode) === null || _script$parentNode === void 0 ? void 0 : _script$parentNode.removeChild(script);\n                script = injectScript(params);\n            }\n            onLoadListener = onLoad(resolve, reject);\n            onErrorListener = onError(reject);\n            script.addEventListener(\"load\", onLoadListener);\n            script.addEventListener(\"error\", onErrorListener);\n        } catch (error) {\n            reject(error);\n            return;\n        }\n    }); // Resets stripePromise on error\n    return stripePromise$1[\"catch\"](function(error) {\n        stripePromise$1 = null;\n        return Promise.reject(error);\n    });\n};\nvar initStripe = function initStripe(maybeStripe, args, startTime) {\n    if (maybeStripe === null) {\n        return null;\n    }\n    var pk = args[0];\n    var isTestKey = pk.match(/^pk_test/); // @ts-expect-error this is not publicly typed\n    var version = runtimeVersionToUrlVersion(maybeStripe.version);\n    var expectedVersion = RELEASE_TRAIN;\n    if (isTestKey && version !== expectedVersion) {\n        console.warn(\"Stripe.js@\".concat(version, \" was loaded on the page, but @stripe/stripe-js@\").concat(\"7.4.0\", \" expected Stripe.js@\").concat(expectedVersion, \". This may result in unexpected behavior. For more information, see https://docs.stripe.com/sdks/stripejs-versioning\"));\n    }\n    var stripe = maybeStripe.apply(undefined, args);\n    registerWrapper(stripe, startTime);\n    return stripe;\n}; // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nvar stripePromise;\nvar loadCalled = false;\nvar getStripePromise = function getStripePromise() {\n    if (stripePromise) {\n        return stripePromise;\n    }\n    stripePromise = loadScript(null)[\"catch\"](function(error) {\n        // clear cache on error\n        stripePromise = null;\n        return Promise.reject(error);\n    });\n    return stripePromise;\n}; // Execute our own script injection after a tick to give users time to do their\n// own script injection.\nPromise.resolve().then(function() {\n    return getStripePromise();\n})[\"catch\"](function(error) {\n    if (!loadCalled) {\n        console.warn(error);\n    }\n});\nvar loadStripe = function loadStripe() {\n    for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n        args[_key] = arguments[_key];\n    }\n    loadCalled = true;\n    var startTime = Date.now(); // if previous attempts are unsuccessful, will re-load script\n    return getStripePromise().then(function(maybeStripe) {\n        return initStripe(maybeStripe, args, startTime);\n    });\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stripe/stripe-js/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stripe/stripe-js/lib/index.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@stripe/stripe-js/lib/index.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadStripe: () => (/* reexport safe */ _dist_index_mjs__WEBPACK_IMPORTED_MODULE_0__.loadStripe)\n/* harmony export */ });\n/* harmony import */ var _dist_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../dist/index.mjs */ \"(ssr)/./node_modules/@stripe/stripe-js/dist/index.mjs\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHN0cmlwZS9zdHJpcGUtanMvbGliL2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFrQyIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9ub2RlX21vZHVsZXMvQHN0cmlwZS9zdHJpcGUtanMvbGliL2luZGV4Lm1qcz84YTY3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4uL2Rpc3QvaW5kZXgubWpzJztcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stripe/stripe-js/lib/index.mjs\n");

/***/ })

};
;