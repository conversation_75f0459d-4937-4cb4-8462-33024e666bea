/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/process-domain-payment/route";
exports.ids = ["app/api/process-domain-payment/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fprocess-domain-payment%2Froute&page=%2Fapi%2Fprocess-domain-payment%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprocess-domain-payment%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fprocess-domain-payment%2Froute&page=%2Fapi%2Fprocess-domain-payment%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprocess-domain-payment%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_dell_Desktop_wp_ai_app_src_app_api_process_domain_payment_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/process-domain-payment/route.ts */ \"(rsc)/./src/app/api/process-domain-payment/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/process-domain-payment/route\",\n        pathname: \"/api/process-domain-payment\",\n        filename: \"route\",\n        bundlePath: \"app/api/process-domain-payment/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Desktop/wp-ai-app/src/app/api/process-domain-payment/route.ts\",\n    nextConfigOutput,\n    userland: _home_dell_Desktop_wp_ai_app_src_app_api_process_domain_payment_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/process-domain-payment/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fprocess-domain-payment%2Froute&page=%2Fapi%2Fprocess-domain-payment%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprocess-domain-payment%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/namecheap/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/namecheap/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   checkDomainAvailability: () => (/* binding */ checkDomainAvailability),\n/* harmony export */   registerDomain: () => (/* binding */ registerDomain)\n/* harmony export */ });\n// app/api/namecheap/route.ts\nconst getNamecheapConfig = ()=>({\n        apiUser: process.env.NAMECHEAP_API_USER || \"\",\n        apiKey: process.env.NAMECHEAP_API_KEY || \"\",\n        username: process.env.NAMECHEAP_USERNAME || \"\",\n        clientIp: process.env.NAMECHEAP_CLIENT_IP || \"\",\n        sandbox: \"development\" !== \"production\"\n    });\nconst getNamecheapBaseUrl = (sandbox)=>{\n    return sandbox ? \"https://api.sandbox.namecheap.com/xml.response\" : \"https://api.namecheap.com/xml.response\";\n};\nconst buildNamecheapUrl = (command, params, config)=>{\n    const baseUrl = getNamecheapBaseUrl(config.sandbox);\n    const baseParams = {\n        ApiUser: config.apiUser,\n        ApiKey: config.apiKey,\n        UserName: config.username,\n        Command: command,\n        ClientIp: config.clientIp,\n        ...params\n    };\n    const queryString = Object.entries(baseParams).map(([key, value])=>`${encodeURIComponent(key)}=${encodeURIComponent(value)}`).join(\"&\");\n    return `${baseUrl}?${queryString}`;\n};\nconst parseXmlResponse = async (xmlText)=>{\n    // Simple regex-based XML parser for server-side use\n    const extractValue = (xml, tag)=>{\n        const regex = new RegExp(`<${tag}[^>]*>([^<]*)</${tag}>`, \"i\");\n        const match = xml.match(regex);\n        return match ? match[1].trim() : \"\";\n    };\n    const extractAttribute = (xml, tag, attr)=>{\n        const regex = new RegExp(`<${tag}[^>]*${attr}=\"([^\"]*)\"`, \"i\");\n        const match = xml.match(regex);\n        return match ? match[1] : \"\";\n    };\n    // Check if the response is successful\n    const status = extractAttribute(xmlText, \"ApiResponse\", \"Status\");\n    if (status !== \"OK\") {\n        const error = extractValue(xmlText, \"Error\");\n        throw new Error(error || \"Unknown API error\");\n    }\n    // Parse domain check results\n    const domainCheckResults = [];\n    const domainRegex = /<DomainCheckResult[^>]*>/gi;\n    let match;\n    while((match = domainRegex.exec(xmlText)) !== null){\n        const domainTag = match[0];\n        const domain = extractAttribute(domainTag, \"DomainCheckResult\", \"Domain\");\n        const available = extractAttribute(domainTag, \"DomainCheckResult\", \"Available\");\n        const isPremium = extractAttribute(domainTag, \"DomainCheckResult\", \"IsPremiumName\");\n        const premiumPrice = extractAttribute(domainTag, \"DomainCheckResult\", \"PremiumRegistrationPrice\");\n        const regularPrice = extractAttribute(domainTag, \"DomainCheckResult\", \"RegularPrice\");\n        domainCheckResults.push({\n            Domain: domain,\n            Available: available,\n            IsPremiumName: isPremium,\n            PremiumRegistrationPrice: premiumPrice,\n            RegularPrice: regularPrice\n        });\n    }\n    return {\n        ApiResponse: {\n            Status: status,\n            CommandResponse: {\n                DomainCheckResult: domainCheckResults\n            }\n        }\n    };\n};\nconst checkDomainAvailability = async (domains)=>{\n    const config = getNamecheapConfig();\n    if (!config.apiUser || !config.apiKey || !config.username || !config.clientIp) {\n        throw new Error(\"Missing required Namecheap API configuration\");\n    }\n    const domainList = domains.join(\",\");\n    const url = buildNamecheapUrl(\"namecheap.domains.check\", {\n        DomainList: domainList\n    }, config);\n    try {\n        const response = await fetch(url, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/x-www-form-urlencoded\"\n            }\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const xmlText = await response.text();\n        const jsonResponse = await parseXmlResponse(xmlText);\n        const apiResponse = jsonResponse.ApiResponse || jsonResponse;\n        if (apiResponse.Status !== \"OK\") {\n            const errorMsg = apiResponse.Errors?.Error || \"Unknown API error\";\n            throw new Error(errorMsg);\n        }\n        const domainCheckResults = apiResponse.CommandResponse?.DomainCheckResult || [];\n        const results = Array.isArray(domainCheckResults) ? domainCheckResults : [\n            domainCheckResults\n        ];\n        const mappedResults = results.map((result)=>{\n            const price = result.IsPremiumName === \"true\" ? parseFloat(result.PremiumRegistrationPrice || \"0\") : parseFloat(result.RegularPrice || \"0\");\n            console.log(\"Domain result:\", {\n                Domain: result.Domain,\n                Available: result.Available === \"true\",\n                IsPremiumName: result.IsPremiumName === \"true\",\n                RawPrice: result.IsPremiumName === \"true\" ? result.PremiumRegistrationPrice : result.RegularPrice,\n                ParsedPrice: price\n            });\n            return {\n                Domain: result.Domain,\n                Available: result.Available === \"true\",\n                IsPremiumName: result.IsPremiumName === \"true\",\n                Price: price || 10\n            };\n        });\n        console.log(\"Final mapped results:\", mappedResults);\n        return mappedResults;\n    } catch (error) {\n        console.error(\"Namecheap API error:\", error);\n        throw new Error(error instanceof Error ? error.message : \"Failed to check domain availability\");\n    }\n};\nconst registerDomain = async (domain, years = 1)=>{\n    const config = getNamecheapConfig();\n    if (!config.apiUser || !config.apiKey || !config.username || !config.clientIp) {\n        throw new Error(\"Missing required Namecheap API configuration\");\n    }\n    const url = buildNamecheapUrl(\"namecheap.domains.create\", {\n        DomainName: domain,\n        Years: years.toString(),\n        // Add default contact information - you should collect this from your form\n        AuxBillingFirstName: \"John\",\n        AuxBillingLastName: \"Doe\",\n        AuxBillingAddress1: \"123 Main St\",\n        AuxBillingCity: \"Anytown\",\n        AuxBillingStateProvince: \"ST\",\n        AuxBillingPostalCode: \"12345\",\n        AuxBillingCountry: \"US\",\n        AuxBillingPhone: \"*************\",\n        AuxBillingEmailAddress: \"<EMAIL>\",\n        // Technical contact (can be same as billing)\n        TechFirstName: \"John\",\n        TechLastName: \"Doe\",\n        TechAddress1: \"123 Main St\",\n        TechCity: \"Anytown\",\n        TechStateProvince: \"ST\",\n        TechPostalCode: \"12345\",\n        TechCountry: \"US\",\n        TechPhone: \"*************\",\n        TechEmailAddress: \"<EMAIL>\",\n        // Admin contact (can be same as billing)\n        AdminFirstName: \"John\",\n        AdminLastName: \"Doe\",\n        AdminAddress1: \"123 Main St\",\n        AdminCity: \"Anytown\",\n        AdminStateProvince: \"ST\",\n        AdminPostalCode: \"12345\",\n        AdminCountry: \"US\",\n        AdminPhone: \"*************\",\n        AdminEmailAddress: \"<EMAIL>\",\n        // Registrant contact (can be same as billing)\n        RegistrantFirstName: \"John\",\n        RegistrantLastName: \"Doe\",\n        RegistrantAddress1: \"123 Main St\",\n        RegistrantCity: \"Anytown\",\n        RegistrantStateProvince: \"ST\",\n        RegistrantPostalCode: \"12345\",\n        RegistrantCountry: \"US\",\n        RegistrantPhone: \"*************\",\n        RegistrantEmailAddress: \"<EMAIL>\"\n    }, config);\n    try {\n        const response = await fetch(url, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/x-www-form-urlencoded\"\n            }\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const xmlText = await response.text();\n        const jsonResponse = await parseXmlResponse(xmlText);\n        const apiResponse = jsonResponse.ApiResponse || jsonResponse;\n        if (apiResponse.Status !== \"OK\") {\n            const errorMsg = apiResponse.Errors?.Error || \"Unknown API error\";\n            throw new Error(errorMsg);\n        }\n        return apiResponse.CommandResponse?.DomainCreateResult;\n    } catch (error) {\n        console.error(\"Namecheap registration error:\", error);\n        throw new Error(error instanceof Error ? error.message : \"Failed to register domain\");\n    }\n};\n// App Router POST handler\nasync function POST(request) {\n    try {\n        const { domain, action = \"check\" } = await request.json();\n        if (!domain) {\n            return Response.json({\n                error: \"Domain name is required\"\n            }, {\n                status: 400\n            });\n        }\n        if (action === \"check\") {\n            const baseDomain = domain.replace(/\\.(com|net|org|io|co|app|dev)$/i, \"\");\n            const tlds = [\n                \"com\",\n                \"net\",\n                \"org\",\n                \"io\",\n                \"co\",\n                \"app\",\n                \"dev\"\n            ];\n            const domainsToCheck = tlds.map((tld)=>`${baseDomain}.${tld}`);\n            const results = await checkDomainAvailability(domainsToCheck);\n            return Response.json({\n                results\n            });\n        } else if (action === \"register\") {\n            const result = await registerDomain(domain);\n            return Response.json({\n                result\n            });\n        } else {\n            return Response.json({\n                error: \"Invalid action\"\n            }, {\n                status: 400\n            });\n        }\n    } catch (error) {\n        console.error(\"API error:\", error);\n        const errorMessage = error instanceof Error ? error.message : \"Internal server error\";\n        return Response.json({\n            error: errorMessage\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/namecheap/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/process-domain-payment/route.ts":
/*!*****************************************************!*\
  !*** ./src/app/api/process-domain-payment/route.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var stripe__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! stripe */ \"(rsc)/./node_modules/stripe/esm/stripe.esm.node.js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _namecheap_route__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../namecheap/route */ \"(rsc)/./src/app/api/namecheap/route.ts\");\n\n\n\n\nconst stripe = new stripe__WEBPACK_IMPORTED_MODULE_1__[\"default\"](process.env.STRIPE_SECRET_KEY, {\n    apiVersion: \"2025-06-30.basil\"\n});\n// Initialize Supabase client\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__.createClient)(\"https://ermaaxnoyckezbjtegmq.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVybWFheG5veWNrZXpianRlZ21xIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMzI2NDgsImV4cCI6MjA2NjkwODY0OH0.ngJdxCneL3KSN-PxlSybKSplflElwH8PKD_J4-_gilI\");\n// Configure DNS with CNAME records\nconst configureDNS = async (domain, siteName)=>{\n    try {\n        // In a production environment, you would use the Namecheap API to set DNS records\n        // This would typically involve:\n        // 1. Setting up a CNAME record for www.domain.com pointing to siteName.yourdomain.com\n        // 2. Setting up an A record for the apex domain (domain.com) pointing to your server IP\n        // Example of what a real implementation might look like:\n        /*\n    const config = {\n      apiUser: process.env.NAMECHEAP_API_USER,\n      apiKey: process.env.NAMECHEAP_API_KEY,\n      username: process.env.NAMECHEAP_USERNAME,\n      clientIp: process.env.NAMECHEAP_CLIENT_IP,\n      sandbox: process.env.NODE_ENV !== 'production',\n    };\n\n    const baseUrl = config.sandbox\n      ? 'https://api.sandbox.namecheap.com/xml.response'\n      : 'https://api.namecheap.com/xml.response';\n\n    // Set CNAME record for www subdomain\n    const cnameDomain = domain.split('.')[0]; // Get the domain name without TLD\n    const tld = domain.substring(cnameDomain.length + 1); // Get the TLD (.com, .org, etc.)\n\n    const params = {\n      ApiUser: config.apiUser,\n      ApiKey: config.apiKey,\n      UserName: config.username,\n      Command: 'namecheap.domains.dns.setHosts',\n      ClientIp: config.clientIp,\n      SLD: cnameDomain,\n      TLD: tld,\n      HostName1: 'www',\n      RecordType1: 'CNAME',\n      Address1: `${siteName}.yourdomain.com`,\n      TTL1: '1800'\n    };\n\n    const queryString = Object.entries(params)\n      .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)\n      .join('&');\n\n    const response = await fetch(`${baseUrl}?${queryString}`);\n    const xmlText = await response.text();\n\n    // Parse XML response to check success\n    const status = xmlText.includes('Status=\"OK\"');\n    return status;\n    */ // For now, we'll simulate success in development\n        console.log(`Configuring DNS for ${domain} to point to ${siteName}`);\n        return true;\n    } catch (error) {\n        console.error(\"DNS configuration error:\", error);\n        return false;\n    }\n};\nasync function POST(req) {\n    try {\n        const { sessionId, domain } = await req.json();\n        if (!sessionId || !domain) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Missing required parameters\"\n            }, {\n                status: 400\n            });\n        }\n        // 1. Verify the Stripe session\n        const session = await stripe.checkout.sessions.retrieve(sessionId);\n        if (session.payment_status !== \"paid\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Payment not completed\"\n            }, {\n                status: 400\n            });\n        }\n        // Extract metadata\n        const siteId = session.metadata?.siteId;\n        if (!siteId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Missing site ID in session metadata\"\n            }, {\n                status: 400\n            });\n        }\n        // 2. Get site information from Supabase\n        const { data: siteData, error: siteError } = await supabase.from(\"user-websites\").select(\"site_name\").eq(\"id\", siteId).single();\n        if (siteError || !siteData) {\n            console.error(\"Site fetch error:\", siteError);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to fetch site information\"\n            }, {\n                status: 500\n            });\n        }\n        // 3. Check domain availability first\n        try {\n            console.log(`Checking availability for domain: ${domain}`);\n            const availabilityResults = await (0,_namecheap_route__WEBPACK_IMPORTED_MODULE_2__.checkDomainAvailability)([\n                domain\n            ]);\n            if (!availabilityResults || availabilityResults.length === 0) {\n                throw new Error(\"Failed to check domain availability\");\n            }\n            const domainResult = availabilityResults[0];\n            console.log(\"Domain availability result:\", domainResult);\n            if (!domainResult.Available) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: `Domain ${domain} is not available for registration. It may have been registered by someone else. Please try a different domain.`,\n                    paymentSuccess: true,\n                    domainUnavailable: true\n                }, {\n                    status: 400\n                });\n            }\n            // 4. Register the domain through Namecheap if it's available\n            console.log(`Registering domain: ${domain}`);\n            const registrationResult = await (0,_namecheap_route__WEBPACK_IMPORTED_MODULE_2__.registerDomain)(domain, 1); // Register for 1 year\n            if (!registrationResult) {\n                throw new Error(\"Domain registration failed\");\n            }\n            // Immediately return success after registration\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: true,\n                message: \"Domain registration completed successfully\"\n            });\n        } catch (regError) {\n            console.error(\"Domain registration error:\", regError);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: `Domain registration failed: ${regError.message}. Payment was successful, but we could not register the domain. Our team will contact you.`,\n                paymentSuccess: true\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error(\"Domain payment processing error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/process-domain-payment/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows","vendor-chunks/stripe","vendor-chunks/qs","vendor-chunks/object-inspect","vendor-chunks/get-intrinsic","vendor-chunks/side-channel-list","vendor-chunks/side-channel-weakmap","vendor-chunks/has-symbols","vendor-chunks/side-channel-map","vendor-chunks/function-bind","vendor-chunks/side-channel","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/call-bound","vendor-chunks/es-errors","vendor-chunks/es-define-property","vendor-chunks/gopd","vendor-chunks/hasown","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fprocess-domain-payment%2Froute&page=%2Fapi%2Fprocess-domain-payment%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprocess-domain-payment%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();