"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/namecheap/route";
exports.ids = ["app/api/namecheap/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnamecheap%2Froute&page=%2Fapi%2Fnamecheap%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnamecheap%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnamecheap%2Froute&page=%2Fapi%2Fnamecheap%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnamecheap%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_dell_Desktop_wp_ai_app_src_app_api_namecheap_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/namecheap/route.ts */ \"(rsc)/./src/app/api/namecheap/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/namecheap/route\",\n        pathname: \"/api/namecheap\",\n        filename: \"route\",\n        bundlePath: \"app/api/namecheap/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Desktop/wp-ai-app/src/app/api/namecheap/route.ts\",\n    nextConfigOutput,\n    userland: _home_dell_Desktop_wp_ai_app_src_app_api_namecheap_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/namecheap/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnamecheap%2Froute&page=%2Fapi%2Fnamecheap%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnamecheap%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/namecheap/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/namecheap/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n\n// Namecheap API configuration\nconst NAMECHEAP_API_URL = \"https://api.sandbox.namecheap.com/xml.response\"; // Use sandbox for testing\n// const NAMECHEAP_API_URL = 'https://api.namecheap.com/xml.response'; // Use this for production\nconst API_USER = process.env.NAMECHEAP_API_USER;\nconst API_KEY = process.env.NAMECHEAP_API_KEY;\nconst USERNAME = process.env.NAMECHEAP_USERNAME;\nconst CLIENT_IP = process.env.NAMECHEAP_CLIENT_IP || \"127.0.0.1\";\n// Helper function to parse XML response (Node.js compatible)\nfunction parseXML(xmlString) {\n    // Simple XML parsing for Node.js environment\n    const parseAttribute = (element, attribute)=>{\n        const regex = new RegExp(`${attribute}=\"([^\"]*)\"`, \"i\");\n        const match = element.match(regex);\n        return match ? match[1] : \"\";\n    };\n    const parseElement = (xmlString, tagName)=>{\n        const regex = new RegExp(`<${tagName}[^>]*>([^<]*)</${tagName}>`, \"gi\");\n        const matches = [];\n        let match;\n        while((match = regex.exec(xmlString)) !== null){\n            matches.push(match[0]);\n        }\n        return matches;\n    };\n    const parseElements = (xmlString, tagName)=>{\n        const regex = new RegExp(`<${tagName}[^>]*(?:/>|>[^<]*</${tagName}>)`, \"gi\");\n        const matches = [];\n        let match;\n        while((match = regex.exec(xmlString)) !== null){\n            matches.push(match[0]);\n        }\n        return matches;\n    };\n    return {\n        getElementsByTagName: (tagName)=>{\n            if (tagName === \"Error\") {\n                return parseElement(xmlString, \"Error\").map((el)=>({\n                        textContent: el.replace(/<[^>]*>/g, \"\")\n                    }));\n            }\n            if (tagName === \"DomainCheckResult\") {\n                return parseElements(xmlString, \"DomainCheckResult\").map((el)=>({\n                        getAttribute: (attr)=>parseAttribute(el, attr)\n                    }));\n            }\n            if (tagName === \"DomainCreateResult\") {\n                const elements = parseElements(xmlString, \"DomainCreateResult\");\n                return elements.length > 0 ? [\n                    {\n                        getAttribute: (attr)=>parseAttribute(elements[0], attr)\n                    }\n                ] : [];\n            }\n            return [];\n        }\n    };\n}\n// Helper function to get common TLDs to check\nfunction getCommonTLDs() {\n    return [\n        \".com\",\n        \".net\",\n        \".org\",\n        \".info\",\n        \".biz\",\n        \".com.au\",\n        \".net.au\",\n        \".org.au\"\n    ];\n}\n// Helper function to extract domain name without TLD\nfunction extractDomainName(fullDomain) {\n    const parts = fullDomain.split(\".\");\n    return parts[0];\n}\n// Helper function to check domain availability\nasync function checkDomainAvailability(domains) {\n    const domainList = domains.join(\",\");\n    const params = new URLSearchParams({\n        ApiUser: API_USER || \"\",\n        ApiKey: API_KEY || \"\",\n        UserName: USERNAME || \"\",\n        Command: \"namecheap.domains.check\",\n        ClientIp: CLIENT_IP,\n        DomainList: domainList\n    });\n    try {\n        const response = await fetch(`${NAMECHEAP_API_URL}?${params}`);\n        const xmlText = await response.text();\n        const xmlDoc = parseXML(xmlText);\n        // Check for API errors\n        const errors = xmlDoc.getElementsByTagName(\"Error\");\n        if (errors.length > 0) {\n            const errorMessage = errors[0].textContent || \"Unknown API error\";\n            throw new Error(errorMessage);\n        }\n        // Parse domain check results\n        const results = [];\n        const domainResults = xmlDoc.getElementsByTagName(\"DomainCheckResult\");\n        for(let i = 0; i < domainResults.length; i++){\n            const domainResult = domainResults[i];\n            const domain = domainResult.getAttribute(\"Domain\") || \"\";\n            const available = domainResult.getAttribute(\"Available\") === \"true\";\n            const isPremium = domainResult.getAttribute(\"IsPremiumName\") === \"true\";\n            // Add 5 AUD profit margin to pricing\n            const basePrice = isPremium ? 99.99 : 12.99;\n            const priceWithMargin = basePrice + 5.00;\n            results.push({\n                Domain: domain,\n                Available: available,\n                IsPremiumName: isPremium,\n                Price: available ? priceWithMargin : undefined\n            });\n        }\n        return results;\n    } catch (error) {\n        console.error(\"Domain check error:\", error);\n        throw error;\n    }\n}\n// Helper function to register a domain\nasync function registerDomain(domain) {\n    const params = new URLSearchParams({\n        ApiUser: API_USER || \"\",\n        ApiKey: API_KEY || \"\",\n        UserName: USERNAME || \"\",\n        Command: \"namecheap.domains.create\",\n        ClientIp: CLIENT_IP,\n        DomainName: domain,\n        Years: \"1\",\n        // Registration contact information (you'll need to customize these)\n        AuxBillingFirstName: \"John\",\n        AuxBillingLastName: \"Doe\",\n        AuxBillingAddress1: \"123 Main St\",\n        AuxBillingCity: \"Anytown\",\n        AuxBillingStateProvince: \"CA\",\n        AuxBillingPostalCode: \"12345\",\n        AuxBillingCountry: \"US\",\n        AuxBillingPhone: \"*************\",\n        AuxBillingEmailAddress: \"<EMAIL>\",\n        // Tech contact (can be same as billing)\n        TechFirstName: \"John\",\n        TechLastName: \"Doe\",\n        TechAddress1: \"123 Main St\",\n        TechCity: \"Anytown\",\n        TechStateProvince: \"CA\",\n        TechPostalCode: \"12345\",\n        TechCountry: \"US\",\n        TechPhone: \"*************\",\n        TechEmailAddress: \"<EMAIL>\",\n        // Admin contact (can be same as billing)\n        AdminFirstName: \"John\",\n        AdminLastName: \"Doe\",\n        AdminAddress1: \"123 Main St\",\n        AdminCity: \"Anytown\",\n        AdminStateProvince: \"CA\",\n        AdminPostalCode: \"12345\",\n        AdminCountry: \"US\",\n        AdminPhone: \"*************\",\n        AdminEmailAddress: \"<EMAIL>\",\n        // Registrant contact (can be same as billing)\n        RegistrantFirstName: \"John\",\n        RegistrantLastName: \"Doe\",\n        RegistrantAddress1: \"123 Main St\",\n        RegistrantCity: \"Anytown\",\n        RegistrantStateProvince: \"CA\",\n        RegistrantPostalCode: \"12345\",\n        RegistrantCountry: \"US\",\n        RegistrantPhone: \"*************\",\n        RegistrantEmailAddress: \"<EMAIL>\"\n    });\n    try {\n        const response = await fetch(`${NAMECHEAP_API_URL}?${params}`);\n        const xmlText = await response.text();\n        const xmlDoc = parseXML(xmlText);\n        // Check for API errors\n        const errors = xmlDoc.getElementsByTagName(\"Error\");\n        if (errors.length > 0) {\n            const errorMessage = errors[0].textContent || \"Unknown API error\";\n            throw new Error(errorMessage);\n        }\n        // Check if registration was successful\n        const domainCreateResult = xmlDoc.getElementsByTagName(\"DomainCreateResult\")[0];\n        if (!domainCreateResult || domainCreateResult.getAttribute(\"Registered\") !== \"true\") {\n            throw new Error(\"Domain registration failed\");\n        }\n    } catch (error) {\n        console.error(\"Domain registration error:\", error);\n        throw error;\n    }\n}\nasync function POST(request) {\n    try {\n        // Check if required environment variables are set\n        if (!API_USER || !API_KEY || !USERNAME) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Missing required Namecheap API credentials\"\n            }, {\n                status: 500\n            });\n        }\n        const { domain, action } = await request.json();\n        if (!domain || !action) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Domain and action are required\"\n            }, {\n                status: 400\n            });\n        }\n        if (action === \"check\") {\n            let domainsToCheck = [];\n            // If domain contains a TLD, check only that domain\n            if (domain.includes(\".\")) {\n                domainsToCheck = [\n                    domain\n                ];\n            } else {\n                // If no TLD provided, check common TLDs\n                const baseDomain = extractDomainName(domain);\n                domainsToCheck = getCommonTLDs().map((tld)=>`${baseDomain}${tld}`);\n            }\n            const results = await checkDomainAvailability(domainsToCheck);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                results\n            });\n        } else if (action === \"register\") {\n            await registerDomain(domain);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: true,\n                message: `Domain ${domain} registered successfully`\n            });\n        } else {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: 'Invalid action. Use \"check\" or \"register\"'\n            }, {\n                status: 400\n            });\n        }\n    } catch (error) {\n        console.error(\"API error:\", error);\n        const errorMessage = error instanceof Error ? error.message : \"Unknown error occurred\";\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: errorMessage\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/namecheap/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnamecheap%2Froute&page=%2Fapi%2Fnamecheap%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnamecheap%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();