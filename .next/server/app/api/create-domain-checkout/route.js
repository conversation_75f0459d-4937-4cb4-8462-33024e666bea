"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/create-domain-checkout/route";
exports.ids = ["app/api/create-domain-checkout/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcreate-domain-checkout%2Froute&page=%2Fapi%2Fcreate-domain-checkout%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcreate-domain-checkout%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcreate-domain-checkout%2Froute&page=%2Fapi%2Fcreate-domain-checkout%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcreate-domain-checkout%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_dell_Desktop_wp_ai_app_src_app_api_create_domain_checkout_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/create-domain-checkout/route.ts */ \"(rsc)/./src/app/api/create-domain-checkout/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/create-domain-checkout/route\",\n        pathname: \"/api/create-domain-checkout\",\n        filename: \"route\",\n        bundlePath: \"app/api/create-domain-checkout/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Desktop/wp-ai-app/src/app/api/create-domain-checkout/route.ts\",\n    nextConfigOutput,\n    userland: _home_dell_Desktop_wp_ai_app_src_app_api_create_domain_checkout_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/create-domain-checkout/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcreate-domain-checkout%2Froute&page=%2Fapi%2Fcreate-domain-checkout%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcreate-domain-checkout%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/create-domain-checkout/route.ts":
/*!*****************************************************!*\
  !*** ./src/app/api/create-domain-checkout/route.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var stripe__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! stripe */ \"(rsc)/./node_modules/stripe/esm/stripe.esm.node.js\");\n\n\nconst stripe = new stripe__WEBPACK_IMPORTED_MODULE_1__[\"default\"](process.env.STRIPE_SECRET_KEY, {\n    apiVersion: \"2025-06-30.basil\"\n});\nasync function POST(req) {\n    try {\n        const { domain, price, siteId, siteName } = await req.json();\n        if (!domain || !price || !siteId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Domain, price, and siteId are required\"\n            }, {\n                status: 400\n            });\n        }\n        const baseUrl = \"http://localhost:3000\" || 0;\n        // Create Stripe checkout session for domain purchase\n        const session = await stripe.checkout.sessions.create({\n            payment_method_types: [\n                \"card\"\n            ],\n            line_items: [\n                {\n                    price_data: {\n                        currency: \"aud\",\n                        product_data: {\n                            name: `Domain Registration: ${domain}`,\n                            description: `One-year registration for ${domain}`,\n                            images: []\n                        },\n                        unit_amount: Math.round(price * 100)\n                    },\n                    quantity: 1\n                }\n            ],\n            mode: \"payment\",\n            success_url: `${baseUrl}/dashboard?domainSuccess=true&domain=${encodeURIComponent(domain)}`,\n            cancel_url: `${baseUrl}/dashboard/domain`,\n            metadata: {\n                type: \"domain_registration\",\n                domain,\n                siteId,\n                siteName: siteName || \"\",\n                price: price.toString()\n            },\n            // Add customer email collection\n            customer_email: undefined,\n            // Optional: Add automatic tax calculation\n            automatic_tax: {\n                enabled: false\n            }\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            sessionId: session.id,\n            url: session.url\n        });\n    } catch (error) {\n        console.error(\"Domain checkout session creation error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: error.message || \"Failed to create checkout session\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/create-domain-checkout/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/stripe","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/qs","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/object-inspect","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/side-channel","vendor-chunks/side-channel-weakmap","vendor-chunks/side-channel-map","vendor-chunks/side-channel-list","vendor-chunks/hasown","vendor-chunks/get-intrinsic","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/call-bound"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcreate-domain-checkout%2Froute&page=%2Fapi%2Fcreate-domain-checkout%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcreate-domain-checkout%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();